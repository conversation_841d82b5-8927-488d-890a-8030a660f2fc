package com.tocc.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * 物资DTO
 * 
 * <AUTHOR>
 */
public class MaterialDTO {
    
    /** 主键ID */
    private String id;

    /** 物资名称 */
    private String materialName;

    /** 类别，0-应急物资，1-应急装备 */
    private String materialType;

    /** 规格型号 */
    private String specModel;

    /** 物资类别 */
    private String categoryCode;

    /** 所属仓库ID */
    private String warehouseId;

    /** 库存数量 */
    private Integer quantity;

    /** 计量单位 */
    private String unit;

    /** 状态(1正常/2待检修/3报废) */
    private Integer status;

    /** 有效期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expiryDate;

    /** 备注信息 */
    private String remark;

    /** 所属救援队伍ID */
    private String teamId;

    /** 创建人 */
    private String creator;

    /** 更新人 */
    private String updater;

    /** 删除标志 */
    private Integer delFlag;

    // Getter and Setter methods
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getMaterialType() {
        return materialType;
    }

    public void setMaterialType(String materialType) {
        this.materialType = materialType;
    }

    public String getSpecModel() {
        return specModel;
    }

    public void setSpecModel(String specModel) {
        this.specModel = specModel;
    }

    public String getCategoryCode() {
        return categoryCode;
    }

    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
    }

    public String getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(String warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(Date expiryDate) {
        this.expiryDate = expiryDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTeamId() {
        return teamId;
    }

    public void setTeamId(String teamId) {
        this.teamId = teamId;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getUpdater() {
        return updater;
    }

    public void setUpdater(String updater) {
        this.updater = updater;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * 验证物资归属的唯一性
     */
    public boolean validateOwnership() {
        boolean hasWarehouse = warehouseId != null && !warehouseId.trim().isEmpty();
        boolean hasTeam = teamId != null && !teamId.trim().isEmpty();

        // 只能归属于仓库或救援队伍中的一个，不能同时归属或都不归属
        return hasWarehouse ^ hasTeam; // 异或：只能有一个为true
    }

    @Override
    public String toString() {
        return "MaterialDTO{" +
                "id='" + id + '\'' +
                ", materialName='" + materialName + '\'' +
                ", materialType='" + materialType + '\'' +
                ", specModel='" + specModel + '\'' +
                ", categoryCode='" + categoryCode + '\'' +
                ", warehouseId='" + warehouseId + '\'' +
                ", quantity=" + quantity +
                ", unit='" + unit + '\'' +
                ", status=" + status +
                ", expiryDate=" + expiryDate +
                ", remark='" + remark + '\'' +
                ", teamId='" + teamId + '\'' +
                ", creator='" + creator + '\'' +
                ", updater='" + updater + '\'' +
                ", delFlag=" + delFlag +
                '}';
    }
}
