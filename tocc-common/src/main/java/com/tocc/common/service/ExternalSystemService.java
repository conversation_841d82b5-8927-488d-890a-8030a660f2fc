package com.tocc.common.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.tocc.common.config.ExternalSystemProperties;
import com.tocc.common.exception.ServiceException;
import com.tocc.common.utils.StringUtils;
import com.tocc.common.utils.http.HttpUtils;
import com.tocc.common.utils.http.HttpClientHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 外部系统服务类
 * 
 * <AUTHOR>
 */
@Service
public class ExternalSystemService 
{
    private static final Logger log = LoggerFactory.getLogger(ExternalSystemService.class);
    
    @Autowired
    private ExternalSystemProperties externalSystemProperties;
    
    @Autowired
    private ExternalDataCacheService cacheService;
    
    @Autowired
    private ExternalAuthManager authManager;
    
    /**
     * 调用外部系统GET接口
     * 
     * @param endpoint 接口端点
     * @param params 请求参数
     * @return 接口响应数据
     */
    public JSONObject callExternalGet(String endpoint, Map<String, Object> params) 
    {
        return callExternalGet(endpoint, params, false);
    }
    
    /**
     * 调用外部系统GET接口
     * 
     * @param endpoint 接口端点
     * @param params 请求参数
     * @param useCache 是否使用缓存
     * @return 接口响应数据
     */
    public JSONObject callExternalGet(String endpoint, Map<String, Object> params, boolean useCache) 
    {
        if (!externalSystemProperties.isEnabled()) 
        {
            throw new ServiceException("外部系统接口未启用");
        }
        
        String cacheKey = null;
        if (useCache) 
        {
            cacheKey = buildCacheKey(endpoint, params);
            JSONObject cachedData = cacheService.getCachedData(cacheKey);
            if (cachedData != null) 
            {
                log.debug("从缓存返回数据: {}", endpoint);
                return cachedData;
            }
        }
        
        try 
        {
            String url = buildUrl(endpoint);
            String paramString = buildParamString(params);
            
            log.info("调用外部系统GET接口: {}", url);
            String response = HttpUtils.sendGet(url, paramString);
            
            if (StringUtils.isEmpty(response)) 
            {
                throw new ServiceException("外部系统返回数据为空");
            }
            
            JSONObject result = JSON.parseObject(response);
            
            // 如果使用缓存，将结果存入缓存
            if (useCache && cacheKey != null) 
            {
                cacheService.setCachedData(cacheKey, result);
            }
            
            return result;
        } 
        catch (Exception e) 
        {
            log.error("调用外部系统GET接口失败: {}", e.getMessage(), e);
            throw new ServiceException("调用外部系统接口失败: " + e.getMessage());
        }
    }
    
    /**
     * 调用外部系统POST接口
     * 
     * @param endpoint 接口端点
     * @param data 请求数据
     * @return 接口响应数据
     */
    public JSONObject callExternalPost(String endpoint, Object data) 
    {
        if (!externalSystemProperties.isEnabled()) 
        {
            throw new ServiceException("外部系统接口未启用");
        }
        
        try 
        {
            String url = buildUrl(endpoint);
            String jsonData = JSON.toJSONString(data);
            
            log.info("调用外部系统POST接口: {}, 数据: {}", url, jsonData);
            String response = HttpUtils.sendPost(url, jsonData, MediaType.APPLICATION_JSON_VALUE);
            
            if (StringUtils.isEmpty(response)) 
            {
                throw new ServiceException("外部系统返回数据为空");
            }
            
            return JSON.parseObject(response);
        } 
        catch (Exception e) 
        {
            log.error("调用外部系统POST接口失败: {}", e.getMessage(), e);
            throw new ServiceException("调用外部系统接口失败: " + e.getMessage());
        }
    }
    
    /**
     * 调用外部系统PUT接口
     * 
     * @param endpoint 接口端点
     * @param data 请求数据
     * @return 接口响应数据
     */
    public JSONObject callExternalPut(String endpoint, Object data) 
    {
        // 由于HttpUtils没有PUT方法，这里使用POST替代
        // 在实际应用中，可以扩展HttpUtils添加PUT方法
        return callExternalPost(endpoint, data);
    }
    
    /**
     * 调用外部系统DELETE接口
     * 
     * @param endpoint 接口端点
     * @param params 请求参数
     * @return 接口响应数据
     */
    public JSONObject callExternalDelete(String endpoint, Map<String, Object> params) 
    {
        // 由于HttpUtils没有DELETE方法，这里使用GET替代
        // 在实际应用中，可以扩展HttpUtils添加DELETE方法
        return callExternalGet(endpoint, params);
    }
    
    /**
     * 构建完整的URL
     * 
     * @param endpoint 接口端点
     * @return 完整URL
     */
    private String buildUrl(String endpoint) 
    {
        String baseUrl = externalSystemProperties.getBaseUrl();
        if (baseUrl.endsWith("/") && endpoint.startsWith("/")) 
        {
            return baseUrl + endpoint.substring(1);
        } 
        else if (!baseUrl.endsWith("/") && !endpoint.startsWith("/")) 
        {
            return baseUrl + "/" + endpoint;
        } 
        else 
        {
            return baseUrl + endpoint;
        }
    }
    
    /**
     * 构建参数字符串
     * 
     * @param params 参数映射
     * @return 参数字符串
     */
    private String buildParamString(Map<String, Object> params) 
    {
        if (params == null || params.isEmpty()) 
        {
            return "";
        }
        
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Object> entry : params.entrySet()) 
        {
            if (sb.length() > 0) 
            {
                sb.append("&");
            }
            sb.append(entry.getKey()).append("=").append(entry.getValue());
        }
        
        // 添加API密钥（如果配置了）
        if (StringUtils.isNotEmpty(externalSystemProperties.getApiKey())) 
        {
            if (sb.length() > 0) 
            {
                sb.append("&");
            }
            sb.append("apiKey=").append(externalSystemProperties.getApiKey());
        }
        
        return sb.toString();
    }
    
    /**
     * 处理外部系统响应数据
     * 
     * @param response 外部系统响应
     * @return 处理后的数据
     */
    public Map<String, Object> processExternalResponse(JSONObject response) 
    {
        Map<String, Object> result = new HashMap<>();
        
        try 
        {
            // 根据外部系统的响应格式进行处理
            
            Integer code = response.getInteger("code");
            String message = response.getString("message");
            Object data = response.get("data");
            
            if (code != null && code == 0)
            {
                result.put("success", true);
                result.put("message", message);
                result.put("data", data);
            } 
            else 
            {
                result.put("success", false);
                result.put("message", message != null ? message : "外部系统返回错误");
                result.put("data", null);
            }
        } 
        catch (Exception e) 
        {
            log.error("处理外部系统响应数据失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "处理外部系统响应数据失败");
            result.put("data", null);
        }
        
        return result;
    }
    
    /**
     * 构建缓存键
     * 
     * @param endpoint 接口端点
     * @param params 请求参数
     * @return 缓存键
     */
    private String buildCacheKey(String endpoint, Map<String, Object> params) 
    {
        StringBuilder sb = new StringBuilder();
        sb.append(endpoint);
        
        if (params != null && !params.isEmpty()) 
        {
            sb.append(":");
            params.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> sb.append(entry.getKey()).append("=").append(entry.getValue()).append(";"));
        }
        
        return sb.toString();
    }
    
    /**
     * 调用需要认证的外部系统GET接口
     * 
     * @param endpoint 接口端点
     * @param params 请求参数
     * @return 接口响应数据
     */
    public JSONObject callAuthenticatedGet(String endpoint, Map<String, Object> params) 
    {
        return callAuthenticatedGet(endpoint, params, false);
    }
    
    /**
     * 调用需要认证的外部系统GET接口
     * 
     * @param endpoint 接口端点
     * @param params 请求参数
     * @param useCache 是否使用缓存
     * @return 接口响应数据
     */
    public JSONObject callAuthenticatedGet(String endpoint, Map<String, Object> params, boolean useCache) 
    {
        String token = authManager.getValidToken();
        if (StringUtils.isEmpty(token)) 
        {
            throw new ServiceException("无法获取有效的认证token");
        }
        
        try 
        {
            String url = buildUrl(endpoint);
            String paramString = buildAuthenticatedParamString(params, token);
            
            log.info("调用需要认证的外部系统GET接口: {}", url);
            String response = HttpUtils.sendGet(url, paramString);

            if (StringUtils.isEmpty(response)) 
            {
                throw new ServiceException("外部系统返回数据为空");
            }
            
            return JSON.parseObject(response);
        } 
        catch (Exception e) 
        {
            log.error("调用需要认证的外部系统GET接口失败: {}", e.getMessage(), e);
            throw new ServiceException("调用外部系统接口失败: " + e.getMessage());
        }
    }
    
    /**
     * 调用需要认证的外部系统POST接口
     * 
     * @param endpoint 接口端点
     * @param data 请求数据
     * @return 接口响应数据
     */
    public JSONObject callAuthenticatedPost(String endpoint, Object data) 
    {
        String token = authManager.getValidToken();
        if (StringUtils.isEmpty(token)) 
        {
            throw new ServiceException("无法获取有效的认证token");
        }
        
        try 
        {
            String url = buildUrl(endpoint);
            String jsonData = JSON.toJSONString(data);
            
            log.info("调用需要认证的外部系统POST接口: {}, 数据: {}", url, jsonData);
            
            // 这里需要增强HttpUtils以支持Header传递，暂时使用现有方式
            // 实际项目中建议扩展HttpUtils支持自定义Header
            String response = HttpUtils.sendPost(url, jsonData, MediaType.APPLICATION_JSON_VALUE);
            
            if (StringUtils.isEmpty(response)) 
            {
                throw new ServiceException("外部系统返回数据为空");
            }
            
            return JSON.parseObject(response);
        } 
        catch (Exception e) 
        {
            log.error("调用需要认证的外部系统POST接口失败: {}", e.getMessage(), e);
            throw new ServiceException("调用外部系统接口失败: " + e.getMessage());
        }
    }
    
    /**
     * 构建带认证信息的参数字符串
     * 
     * @param params 参数映射
     * @param token 认证token
     * @return 参数字符串
     */
    private String buildAuthenticatedParamString(Map<String, Object> params, String token) 
    {
        StringBuilder sb = new StringBuilder();
        
        if (params != null && !params.isEmpty()) 
        {
            for (Map.Entry<String, Object> entry : params.entrySet()) 
            {
                if (sb.length() > 0) 
                {
                    sb.append("&");
                }
                sb.append(entry.getKey()).append("=").append(entry.getValue());
            }
        }
        
        // 添加token（可以作为参数或者Header，这里先作为参数）
        if (sb.length() > 0) 
        {
            sb.append("&");
        }
        sb.append("Authorization=Bearer ").append(token);
        
        // 添加API密钥（如果配置了）
        if (StringUtils.isNotEmpty(externalSystemProperties.getApiKey())) 
        {
            sb.append("&apiKey=").append(externalSystemProperties.getApiKey());
        }
        
        return sb.toString();
    }
    
    /**
     * 调用外部系统接口（支持自定义请求头）- GET方法
     * 
     * @param endpoint 接口端点
     * @param headers 自定义请求头
     * @param params 请求参数
     * @param useAuth 是否使用认证token
     * @param useCache 是否使用缓存
     * @return 接口响应数据
     */
    public JSONObject callExternalWithHeaders(String endpoint, Map<String, String> headers, 
                                             Map<String, Object> params, boolean useAuth, boolean useCache) 
    {
        return callExternalWithHeaders("GET", endpoint, headers, params, null, useAuth, useCache);
    }
    
    /**
     * 调用外部系统接口（支持自定义请求头）- POST方法
     * 
     * @param endpoint 接口端点
     * @param headers 自定义请求头
     * @param data 请求数据
     * @param useAuth 是否使用认证token
     * @return 接口响应数据
     */
    public JSONObject callExternalPostWithHeaders(String endpoint, Map<String, String> headers, 
                                                 Object data, boolean useAuth) 
    {
        return callExternalWithHeaders("POST", endpoint, headers, null, data, useAuth, false);
    }
    
    /**
     * 通用的外部系统请求方法（支持自定义请求头）
     * 
     * @param method HTTP方法（GET, POST）
     * @param endpoint 接口端点
     * @param headers 自定义请求头
     * @param params 请求参数（GET方法使用）
     * @param data 请求数据（POST方法使用）
     * @param useAuth 是否使用认证token
     * @param useCache 是否使用缓存（仅GET方法有效）
     * @return 接口响应数据
     */
    public JSONObject callExternalWithHeaders(String method, String endpoint, Map<String, String> headers,
                                             Map<String, Object> params, Object data, 
                                             boolean useAuth, boolean useCache) 
    {
        if (!externalSystemProperties.isEnabled()) 
        {
            throw new ServiceException("外部系统接口未启用");
        }
        
        try 
        {
            String url = buildUrl(endpoint);
            
            // 处理自定义请求头
            Map<String, String> finalHeaders = new HashMap<>();
            if (headers != null) 
            {
                finalHeaders.putAll(headers);
            }
            
            // 如果需要认证，添加token到请求头
            if (useAuth) 
            {
                String token = authManager.getValidToken();
                if (StringUtils.isNotEmpty(token)) 
                {
                    finalHeaders.put("Authorization", "Bearer " + token);
                } 
                else 
                {
                    throw new ServiceException("无法获取有效的认证token");
                }
            }
            
            // 检查缓存（仅对GET方法有效）
            String cacheKey = null;
            if (useCache && "GET".equalsIgnoreCase(method)) 
            {
                cacheKey = buildCacheKey(endpoint, params);
                JSONObject cachedData = cacheService.getCachedData(cacheKey);
                if (cachedData != null) 
                {
                    log.debug("从缓存返回数据: {}", endpoint);
                    return cachedData;
                }
            }
            
            String response = null;
            
            if ("GET".equalsIgnoreCase(method)) 
            {
                // GET请求：将参数拼接到URL
                if (params != null && !params.isEmpty()) 
                {
                    String paramString = buildParamString(params);
                    if (StringUtils.isNotEmpty(paramString)) 
                    {
                        url += (url.contains("?") ? "&" : "?") + paramString;
                    }
                }
                
                log.info("调用外部系统GET接口: {}", url);
                response = HttpClientHelper.get(url, finalHeaders, externalSystemProperties.getReadTimeout());
            } 
            else if ("POST".equalsIgnoreCase(method)) 
            {
                log.info("调用外部系统POST接口: {}", url);
                
                if (data != null) 
                {
                    String jsonData = data instanceof String ? (String) data : JSON.toJSONString(data);
                    response = HttpClientHelper.post(url, finalHeaders, jsonData);
                } 
                else 
                {
                    response = HttpClientHelper.post(url, finalHeaders, "{}");
                }
            } 
            else 
            {
                throw new ServiceException("不支持的HTTP方法: " + method);
            }
            
            if (StringUtils.isEmpty(response)) 
            {
                throw new ServiceException("外部系统返回数据为空");
            }
            
            JSONObject result = JSON.parseObject(response);
            
            // 如果使用缓存，将结果存入缓存
            if (useCache && cacheKey != null && "GET".equalsIgnoreCase(method)) 
            {
                cacheService.setCachedData(cacheKey, result);
            }
            
            return result;
            
        } 
        catch (Exception e) 
        {
            log.error("调用外部系统接口失败: {}", e.getMessage(), e);
            throw new ServiceException("调用外部系统接口失败: " + e.getMessage());
        }
    }
} 