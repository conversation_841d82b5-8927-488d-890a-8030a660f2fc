# 物资管理接口文档

## 概述

物资管理模块提供应急物资和应急装备的完整CRUD功能，支持物资与仓库、救援队伍的关联管理。

### 业务规则

1. **物资归属唯一性**：每个物资只能归属于一个仓库或一个救援队伍，不能同时归属或都不归属
2. **物资类型**：
   - `0` - 应急物资
   - `1` - 应急装备
3. **状态管理**：
   - `1` - 正常
   - `2` - 待检修
   - `3` - 报废

## 接口列表

### 1. 查询物资列表

**接口地址：** `GET /material/list`

**权限要求：** `material:list`

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNum | int | 否 | 页码，默认1 |
| pageSize | int | 否 | 每页数量，默认10 |
| materialName | string | 否 | 物资名称（模糊查询） |
| materialType | string | 否 | 物资类型（0-应急物资，1-应急装备） |
| warehouseId | string | 否 | 仓库ID |
| teamId | string | 否 | 救援队伍ID |

**请求示例：**
```
GET /material/list?pageNum=1&pageSize=10&materialName=救生&materialType=0
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "id": "material001",
      "materialName": "救生衣",
      "materialType": "0",
      "materialTypeName": "应急物资",
      "specModel": "XL",
      "categoryCode": "RESCUE_001",
      "warehouseId": "warehouse001",
      "warehouseName": "中心仓库",
      "teamId": null,
      "teamName": null,
      "quantity": 100,
      "unit": "件",
      "status": 1,
      "statusName": "正常",
      "expiryDate": "2025-12-31",
      "remark": "新采购",
      "createTime": "2024-01-01 10:00:00",
      "creator": "admin",
      "updateTime": "2024-01-01 10:00:00",
      "updater": "admin"
    }
  ],
  "total": 1
}
```

### 2. 新增物资

**接口地址：** `POST /material`

**权限要求：** `material:add`

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| materialName | string | 是 | 物资名称 |
| materialType | string | 是 | 物资类型（0-应急物资，1-应急装备） |
| specModel | string | 否 | 规格型号 |
| categoryCode | string | 否 | 物资类别编码 |
| warehouseId | string | 否 | 所属仓库ID（与teamId二选一） |
| teamId | string | 否 | 所属救援队伍ID（与warehouseId二选一） |
| quantity | int | 否 | 库存数量 |
| unit | string | 否 | 计量单位 |
| status | int | 否 | 状态（1正常/2待检修/3报废），默认1 |
| expiryDate | string | 否 | 有效期（格式：yyyy-MM-dd） |
| remark | string | 否 | 备注信息 |

**请求示例：**
```json
{
  "materialName": "救生衣",
  "materialType": "0",
  "specModel": "XL",
  "categoryCode": "RESCUE_001",
  "warehouseId": "warehouse001",
  "quantity": 100,
  "unit": "件",
  "status": 1,
  "expiryDate": "2025-12-31",
  "remark": "新采购"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功"
}
```

### 3. 修改物资

**接口地址：** `PUT /material`

**权限要求：** `material:edit`

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | string | 是 | 物资ID |
| materialName | string | 否 | 物资名称 |
| materialType | string | 否 | 物资类型（0-应急物资，1-应急装备） |
| specModel | string | 否 | 规格型号 |
| categoryCode | string | 否 | 物资类别编码 |
| warehouseId | string | 否 | 所属仓库ID（与teamId二选一） |
| teamId | string | 否 | 所属救援队伍ID（与warehouseId二选一） |
| quantity | int | 否 | 库存数量 |
| unit | string | 否 | 计量单位 |
| status | int | 否 | 状态（1正常/2待检修/3报废） |
| expiryDate | string | 否 | 有效期（格式：yyyy-MM-dd） |
| remark | string | 否 | 备注信息 |

**请求示例：**
```json
{
  "id": "material001",
  "materialName": "救生衣（加强版）",
  "quantity": 120,
  "remark": "更新库存"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功"
}
```

### 4. 删除物资

**接口地址：** `DELETE /material/{ids}`

**权限要求：** `material:remove`

**路径参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ids | string | 是 | 物资ID，多个用逗号分隔 |

**请求示例：**
```
DELETE /material/material001,material002
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功"
}
```

## 数据字典

### 物资类型 (materialType)

| 值 | 说明 |
|----|------|
| 0 | 应急物资 |
| 1 | 应急装备 |

### 物资状态 (status)

| 值 | 说明 |
|----|------|
| 1 | 正常 |
| 2 | 待检修 |
| 3 | 报废 |

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 500 | 系统错误 |
| 401 | 未授权 |
| 403 | 权限不足 |

## 特殊说明

### 物资归属验证

在新增和修改物资时，系统会验证物资归属的唯一性：

1. **必须指定归属**：`warehouseId` 和 `teamId` 必须有且仅有一个不为空
2. **不能同时归属**：不能同时指定仓库和救援队伍
3. **不能都不归属**：不能两个都为空

**错误示例：**
```json
// 错误：同时指定了仓库和救援队伍
{
  "materialName": "救生衣",
  "materialType": "0",
  "warehouseId": "warehouse001",
  "teamId": "team001"  // 错误：不能同时指定
}

// 错误：都没有指定归属
{
  "materialName": "救生衣",
  "materialType": "0"
  // 错误：warehouseId 和 teamId 都为空
}
```

### 查询筛选说明

1. **名称查询**：支持模糊查询，会匹配物资名称中包含关键字的记录
2. **类型筛选**：可以通过 `materialType` 参数筛选特定类型的物资
3. **归属筛选**：可以通过 `warehouseId` 或 `teamId` 查询特定仓库或救援队伍的物资
4. **组合查询**：支持多个条件组合查询

### 返回数据说明

1. **关联信息**：返回数据中包含关联的仓库名称 (`warehouseName`) 和救援队伍名称 (`teamName`)
2. **字典转换**：`materialTypeName` 和 `statusName` 字段已转换为中文描述
3. **时间格式**：所有时间字段统一使用 `yyyy-MM-dd HH:mm:ss` 格式
4. **分页信息**：列表查询返回 `total` 字段表示总记录数

## 前端开发建议

### 表单验证

```javascript
// 物资归属验证
function validateOwnership(formData) {
  const hasWarehouse = formData.warehouseId && formData.warehouseId.trim();
  const hasTeam = formData.teamId && formData.teamId.trim();
  
  if (hasWarehouse && hasTeam) {
    return { valid: false, message: '物资不能同时归属于仓库和救援队伍' };
  }
  
  if (!hasWarehouse && !hasTeam) {
    return { valid: false, message: '物资必须归属于一个仓库或救援队伍' };
  }
  
  return { valid: true };
}
```

### 类型筛选组件

```javascript
// 物资类型选项
const materialTypeOptions = [
  { value: '', label: '全部' },
  { value: '0', label: '应急物资' },
  { value: '1', label: '应急装备' }
];

// 状态选项
const statusOptions = [
  { value: 1, label: '正常' },
  { value: 2, label: '待检修' },
  { value: 3, label: '报废' }
];
```
