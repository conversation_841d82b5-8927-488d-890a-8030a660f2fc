<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>tocc</artifactId>
        <groupId>com.tocc</groupId>
        <version>3.8.9</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>tocc-admin</artifactId>

    <description>
        web服务入口
    </description>

    <dependencies>

        <!-- spring-boot-devtools -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional> <!-- 表示依赖不会传递 -->
        </dependency>

        <!-- swagger3-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
        </dependency>

        <!-- 防止进入swagger页面报类型转换错误，排除3.0.0中的引用，手动增加1.6.2版本 -->
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
            <version>1.6.2</version>
        </dependency>

         <!-- Mysql驱动包 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!-- 核心模块-->
        <dependency>
            <groupId>com.tocc</groupId>
            <artifactId>tocc-framework</artifactId>
        </dependency>

        <!-- 定时任务-->
        <dependency>
            <groupId>com.tocc</groupId>
            <artifactId>tocc-quartz</artifactId>
        </dependency>

        <!-- 代码生成-->
        <dependency>
            <groupId>com.tocc</groupId>
            <artifactId>tocc-generator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tocc</groupId>
            <artifactId>tocc-system</artifactId>
        </dependency>

        <!-- 电话模块-->
        <dependency>
            <groupId>com.tocc</groupId>
            <artifactId>tocc-tel</artifactId>
        </dependency>

        <!-- 应急模块-->
        <dependency>
            <groupId>com.tocc</groupId>
            <artifactId>tocc-emer</artifactId>
        </dependency>

        <!-- 报警模块-->
        <dependency>
            <groupId>com.tocc</groupId>
            <artifactId>tocc-alarm</artifactId>
        </dependency>

        <!-- 气象预警模块-->
        <dependency>
            <groupId>com.tocc</groupId>
            <artifactId>tocc-weather</artifactId>
        </dependency>

        <!-- 视频会议模块-->
        <dependency>
            <groupId>com.tocc</groupId>
            <artifactId>tocc-live</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.5.15</version>
                <configuration>
                    <fork>true</fork> <!-- 如果没有该配置，devtools不会生效 -->
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>   
                <groupId>org.apache.maven.plugins</groupId>   
                <artifactId>maven-war-plugin</artifactId>   
                <version>3.1.0</version>   
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    <warName>${project.artifactId}</warName>
                </configuration>   
           </plugin>   
        </plugins>
        <finalName>${project.artifactId}</finalName>
    </build>

</project>