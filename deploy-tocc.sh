#!/bin/bash

# 停止 tocc-admin.jar 进程
PID=$(ps -ef|grep tocc-admin.jar|grep -v grep|awk '{print $2}')
if [ -n "$PID" ]; then
    echo "killing tocc-admin.jar"
    echo $PID
    kill -15 $PID
    sleep 2
    kill -9 $PID > /dev/null 2>&1
    echo "kill $PID end"
fi

# 停止启动脚本进程（如果有的话）
PID=$(ps -ef|grep tocc-start.sh|grep -v grep|awk '{print $2}')
if [ -n "$PID" ]; then
    echo "killing tocc-start.sh"
    echo $PID
    kill -15 $PID
    sleep 2
    kill -9 $PID > /dev/null 2>&1
    echo "kill $PID end"
fi

echo "restarting tocc-admin.jar"
echo "tail -f /home/<USER>/tocc-log.out"

# 启动应用
cd /home/<USER>
nohup java -jar tocc-admin.jar > tocc-log.out 2>&1 &
